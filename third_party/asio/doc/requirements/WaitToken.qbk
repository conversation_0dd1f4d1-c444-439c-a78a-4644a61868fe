[/
 / Copyright (c) 2003-2025 Christopher <PERSON> (chris at kohlhoff dot com)
 /
 / Distributed under the Boost Software License, Version 1.0. (See accompanying
 / file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
 /]

[section:WaitToken Wait token requirements]

A wait token is a [link boost_asio.overview.model.completion_tokens completion token]
for completion signature `void(error_code)`.

[heading Examples]

A free function as a wait token:

  void wait_handler(
      const boost::system::error_code& ec)
  {
    ...
  }

A wait token function object:

  struct wait_handler
  {
    ...
    void operator()(
        const boost::system::error_code& ec)
    {
      ...
    }
    ...
  };

A lambda as a wait token:

  socket.async_wait(...,
      [](const boost::system::error_code& ec)
      {
        ...
      });

A non-static class member function adapted to a wait token using
`std::bind()`:

  void my_class::wait_handler(
      const boost::system::error_code& ec)
  {
    ...
  }
  ...
  socket.async_wait(...,
      std::bind(&my_class::wait_handler,
        this, std::placeholders::_1));

A non-static class member function adapted to a wait token using
`boost::bind()`:

  void my_class::wait_handler(
      const boost::system::error_code& ec)
  {
    ...
  }
  ...
  socket.async_wait(...,
      boost::bind(&my_class::wait_handler,
        this, boost::asio::placeholders::error));

Using [link boost_asio.reference.use_future use_future] as a wait token:

  std::future<void> f = socket.async_wait(..., boost::asio::use_future);
  ...
  try
  {
    f.get();
  }
  catch (const system_error& e)
  {
    ...
  }

Using [link boost_asio.reference.use_awaitable use_awaitable] as a wait token:

  boost::asio::awaitable<void> my_coroutine()
  {
    try
    {
      ...
      co_await socket.async_wait(..., boost::asio::use_awaitable);
      ...
    }
    catch (const system_error& e)
    {
      ...
    }
  }

[endsect]
