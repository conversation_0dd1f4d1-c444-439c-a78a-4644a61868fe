[/
 / Copyright (c) 2003-2025 <PERSON> (chris at kohlhoff dot com)
 /
 / Distributed under the Boost Software License, Version 1.0. (See accompanying
 / file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
 /]

[section:WaitHandler Wait handler requirements]

A wait handler must meet the requirements for a [link
boost_asio.reference.Handler handler]. A value `h` of a wait handler class
should work correctly in the expression `h(ec)`, where `ec` is an lvalue of
type `const error_code`.

[endsect]
