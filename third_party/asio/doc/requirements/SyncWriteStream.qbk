[/
 / Copyright (c) 2003-2025 <PERSON> (chris at kohlhoff dot com)
 /
 / Distributed under the Boost Software License, Version 1.0. (See accompanying
 / file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
 /]

[section:SyncWriteStream Buffer-oriented synchronous write stream requirements]

A type `X` meets the `SyncWriteStream` requirements if it satisfies the
requirements listed below.

In the table below, `a` denotes a value of type `X`, `cb` denotes a (possibly
const) value satisfying the [link boost_asio.reference.ConstBufferSequence
`ConstBufferSequence`] requirements, and `ec` denotes an object of type
`error_code`.

[table SyncWriteStream requirements
  [[operation] [type] [semantics, pre/post-conditions]]
  [
    [`a.write_some(cb)`[br]
     `a.write_some(cb,ec)`]
    [`size_t`]
    [
      Meets the requirements for a [link boost_asio.reference.read_write_operations
      write operation].[br]
      [br]
      If `buffer_size(cb) > 0`, writes one or more bytes of data to the stream
      `a` from the buffer sequence `cb`. If successful, sets `ec` such that
      `!ec` is `true`, and returns the number of bytes written. If an error
      occurred, sets `ec` such that `!!ec` is `true`, and returns 0.[br]
      [br]
      If `buffer_size(cb) == 0`, the operation shall not block. Sets `ec` such
      that `!ec` is `true`, and returns 0.
    ]
  ]
]

[endsect]
