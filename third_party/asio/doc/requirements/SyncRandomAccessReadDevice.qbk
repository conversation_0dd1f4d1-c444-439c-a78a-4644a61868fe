[/
 / Copyright (c) 2003-2025 Christopher <PERSON> (chris at kohlhoff dot com)
 /
 / Distributed under the Boost Software License, Version 1.0. (See accompanying
 / file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
 /]

[section:SyncRandomAccessReadDevice Buffer-oriented synchronous random-access
read device requirements]

In the table below, `a` denotes a synchronous random-access read device object,
`o` denotes an offset of type `boost::uint64_t`, `mb` denotes an object
satisfying [link boost_asio.reference.MutableBufferSequence mutable buffer sequence]
requirements, and `ec` denotes an object of type `error_code`.

[table Buffer-oriented synchronous random-access read device requirements
  [[operation] [type] [semantics, pre/post-conditions]]
  [
    [`a.read_some_at(o, mb);`]
    [`size_t`]
    [Equivalent to:
     ``
       error_code ec;
       size_t s = a.read_some_at(o, mb, ec);
       if (ec) throw system_error(ec);
       return s;
     ``]
  ]
  [
    [`a.read_some_at(o, mb, ec);`]
    [`size_t`]
    [
      Reads one or more bytes of data from the device `a` at offset `o`.[br]
      [br]
      The mutable buffer sequence `mb` specifies memory where the data should
      be placed. The `read_some_at` operation shall always fill a buffer in the
      sequence completely before proceeding to the next.[br]
      [br]
      If successful, returns the number of bytes read and sets `ec` such that
      `!ec` is true. If an error occurred, returns `0` and sets `ec` such that
      `!!ec` is true.[br]
      [br]
      If the total size of all buffers in the sequence `mb` is `0`, the
      function shall return `0` immediately.
    ]
  ]
]

[endsect]
