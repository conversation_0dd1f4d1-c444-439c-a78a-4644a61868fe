[/
 / Copyright (c) 2003-2025 <PERSON> (chris at kohlhoff dot com)
 /
 / Distributed under the Boost Software License, Version 1.0. (See accompanying
 / file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
 /]

[section:WriteHandler Write handler requirements]

A write handler must meet the requirements for a [link
boost_asio.reference.Handler handler]. A value `h` of a write handler class
should work correctly in the expression `h(ec, s)`, where `ec` is an lvalue of
type `const error_code` and `s` is an lvalue of type `const size_t`.

[endsect]
