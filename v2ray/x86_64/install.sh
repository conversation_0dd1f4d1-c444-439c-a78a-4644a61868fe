# proxychains4 wget  https://github.com/v2fly/v2ray-core/releases/download/v5.37.0/v2ray-linux-64.zip
# proxychains4 wget https://github.com/v2rayA/v2rayA/releases/download/v2.2.6.7/installer_archlinux_x64_2.2.6.7.pkg.tar.zst
unzip v2ray-linux-64.zip -d /usr/local/v2ray-core
mkdir -p /usr/local/share/v2ray/
mv /usr/local/v2ray-core/*dat /usr/local/share/v2ray/
pacman -U installer_archlinux_x64_2.2.6.7.pkg.tar.zst
# 自动配置 v2raya
# 备份原配置文件
cp /etc/default/v2raya /etc/default/v2raya.bak
# 在原文件开头插入新配置
(echo "V2RAYA_V2RAY_BIN=/usr/local/v2ray-core/v2ray"; echo "V2RAYA_V2RAY_CONFDIR=/usr/local/v2ray-core"; cat /etc/default/v2raya.bak) > /etc/default/v2raya
systemctl enable --now v2raya
systemctl status v2raya
