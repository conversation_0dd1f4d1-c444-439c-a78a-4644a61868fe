#!/bin/bash

# 定义 home 变量
home=$(pwd)

# 定义命令到包名的映射关系（pacman）
declare -A PACMAN_PKGS=(
    ["proxychains4"]="proxychains-ng"
    ["unzip"]="unzip"
    ["vim"]="vim"
    ["git"]="git"
    ["curl"]="curl"
    ["wget"]="wget"
    ["cmake"]="cmake"
    ["make"]="make"
    ["gcc"]="gcc"
    ["g++"]="g++"
    ["go"]="go"
    ["golangci-lint"]="golangci-lint"
    ["rust"]="rust"
    # 在这里添加更多映射
)

# 定义需要用 paru 安装的包（命令名到包名的映射）
declare -A PARU_PKGS=(
    ["mariadb"]="mariadb"
    ["conan"]="conan"
    ["pnpm"]="pnpm"
    ["npm"]="npm"
    # 在这里添加更多需要用 paru 安装的包
)

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项] ..."
    echo "选项:"
    echo "  -init              初始化环境（安装依赖、配置镜像源等）"
    echo "  -cpp <name>        创建新的 cpp 项目，项目名为 <name>"
    echo "  -go <name>         创建新的 Go 项目，项目名为 <name>"
    echo "  -h, --help         显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 -init                     # 仅初始化环境"
    echo "  $0 -cpp myproject            # 仅创建 cpp 项目"
    echo
    echo "注意:"
    echo "  - 选项可以组合使用"
    echo "  - -init 会安装所需依赖并配置镜像源"
    echo "  - -cpp 会从 ./cpp 目录复制项目模板"
    echo "  - -go 会从 ./go 目录复制项目模板"
}

# 配置 pacman 镜像源
configure_mirror() {
    local mirror_file="/etc/pacman.d/mirrorlist"
    local ustc_mirror="Server = https://mirrors.ustc.edu.cn/archlinux/\$repo/os/\$arch"

    # 备份原文件
    sudo cp "$mirror_file" "${mirror_file}.backup"
    
    sudo sed -i '/mirrors/d' "$mirror_file"
    # 在文件开头添加 USTC 镜像
    echo -e "${ustc_mirror}\n$(cat ${mirror_file})" | sudo tee "$mirror_file" > /dev/null
    
    # 更新 pacman 数据库（强制更新因为更换了镜像源）
    sudo pacman -Syy
}

proxychains_config() {
    sudo cp /etc/proxychains.conf /etc/proxychains.conf.backup
    sudo sed -i 's/^socks4/#socks4/g' /etc/proxychains.conf
    sudo sed -i 's/^socks5/#socks5/g' /etc/proxychains.conf
    # 添加新的 socks5 代理设置
    echo "socks5 192.168.2.10 10808" | sudo tee -a /etc/proxychains.conf
}

# 检查并安装 pacman 依赖
install_pacman_packages() {
    local missing_deps=()
    
    # 检查所有依赖
    for cmd in "${!PACMAN_PKGS[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            local pkg="${PACMAN_PKGS[$cmd]}"
            echo "检测到命令 '$cmd' 未安装 (包名: $pkg)"
            missing_deps+=("$pkg")
        fi
    done
    
    # 如果有缺失的依赖，则安装
    if [ ${#missing_deps[@]} -ne 0 ]; then
        echo "将安装以下包："
        printf '%s\n' "${missing_deps[@]}"
        sudo pacman -S "${missing_deps[@]}"  # 这里不需要 -y 因为已经在 configure_mirror 中更新过了
    else
        echo "所有 pacman 依赖已安装"
    fi
}

# 安装paru
install_paru() {
    proxychains4 git clone https://aur.archlinux.org/paru.git
    cd paru
    proxychains4 makepkg -si
    cd ..
    rm -rf paru
}

# 使用 paru 安装其他依赖
install_paru_packages() {
    local missing_deps=()
    
    # 检查所有 paru 依赖
    for cmd in "${!PARU_PKGS[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            local pkg="${PARU_PKGS[$cmd]}"
            echo "检测到命令 '$cmd' 未安装 (包名: $pkg)"
            missing_deps+=("$pkg")
        fi
    done
    
    # 如果有缺失的依赖，则安装
    if [ ${#missing_deps[@]} -ne 0 ]; then
        echo "将通过 paru 安装以下包："
        printf '%s\n' "${missing_deps[@]}"
        echo "是否继续安装？(y/n)"
        read -r install_choice
        if [[ "$install_choice" =~ ^[Yy]$ ]]; then
            for pkg in "${missing_deps[@]}"; do
                proxychains4 paru -S "$pkg"
            done
        else
            echo "用户取消安装"
            exit 1
        fi
    else
        echo "所有 paru 依赖已安装"
    fi
}

# 初始化环境
init_environment() {
    configure_mirror 
    install_pacman_packages
    proxychains_config
    install_paru
    install_paru_packages
    
    conan profile detect --force
    go env -w GOPROXY=https://goproxy.cn,direct
    pnpm config set registry https://registry.npmmirror.com
    cp "${home}/.ssh/"* ~/.ssh
    mysql_install_db --user=mysql --basedir=/usr --datadir=/var/lib/mysql
}

# 创建 cpp 项目
create_cpp_project() {
    local project_name="$1"
    if [ -z "$project_name" ]; then
        echo "错误：未指定项目名称"
        exit 1
    fi

    if [ -d "${home}/$project_name" ]; then
        echo "错误：目录 '$project_name' 已存在"
        exit 1
    fi

    # 复制模板目录
    cp -r "${home}/cpp" "${home}/$project_name"
    
    # 替换项目名称
    find "${home}/$project_name" -type f -exec sed -i "s/example-zexuan/$project_name/g" {} \;
    
    echo "已创建 cpp 项目：$project_name"
}



# 主程序
main() {
    # 如果没有参数，显示帮助
    if [ $# -eq 0 ]; then
        show_help
        exit 1
    fi

    # 处理命令行参数
    while [ $# -gt 0 ]; do
        case "$1" in
            -init)
                init_environment
                ;;
            -cpp)
                if [ -z "$2" ]; then
                    echo "错误：-cpp 选项需要指定项目名称"
                    exit 1
                fi
                create_cpp_project "$2"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                echo "错误：未知选项 $1"
                show_help
                exit 1
                ;;
        esac
        shift
    done
}

main "$@"